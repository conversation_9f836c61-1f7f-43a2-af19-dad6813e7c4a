import OpenAI from 'openai';
import { CDP } from '../browser/simple-cdp';
import { WorkflowEntrypoint, WorkflowStep, type WorkflowEvent } from 'cloudflare:workers';
import {
  instructions,
  platformLoginLinks,
  defaultWorkflowNoRetryConfig,
  defaultWorkflowRetryConfig,
  K_CUSTOM_VIEWPORT,
} from './utils/constants';
import { extractFormJSON } from './utils/helpers';
import { Environment } from '../common/types';
import {
  ConnectionsWorkflowParams,
  FormSubmissionEventPayload,
} from './types/ConnectionsWorkflowParams';
import { Action, PageStateResult } from '../agent/types/extract-result';
import { decryptData, getEncryptionKey } from '../common/utils';
import {
  initRTCSteaming,
  initScreenCropper,
  initBrowserController,
  initControlTabBrowserController,
  initTargetTabProxyController,
  injectScript,
  getHashedScriptUrl,
  initTensorFlowDetector,
  injectTensorFlowJS,
  setupInputFocusListener,
} from '../browser';
import {
  ConnectionWorkflowState,
  storeConnectionScreenshotToR2,
} from '../common/utils/storeConnectionScreenshotToR2';
import { PlatformTypes } from '../ui/constants';
import { CDPBrowserDataAdapter } from './adapters/CDPBrowserDataAdapter';
import { BrowserStateService } from './BrowserStateService';
import { R2BrowserStateRepository } from './R2BrowserStateRepository';
import { BrowserServiceFactory, RemoteBrowserService } from './services';
import { raceLLMCalls } from '../llm/llm-call';
import { testLoginCaptcha1 } from '../mock';

export class ConnectionsWorkflow extends WorkflowEntrypoint<
  Environment,
  ConnectionsWorkflowParams
> {
  private cdp?: CDP;
  private targetSessionId?: string;
  private targetId?: string;
  private browserStateService: BrowserStateService = new BrowserStateService(
    new R2BrowserStateRepository(this.env.SCREENSHOTS_INBOUND_BUCKET),
  );
  private browserService: RemoteBrowserService = BrowserServiceFactory.createFromEnvironment(
    this.env,
  );

  // Two-tab architecture properties
  private useTwoTabArchitecture: boolean = false;
  private controlTabSessionId?: string;
  private controlTabId?: string;
  private controlExecutionContextId?: number;
  private targetExecutionContextId?: number;

  /**
   * Enable two-tab architecture for crash prevention
   * Call this before running the workflow to use the new architecture
   */
  enableTwoTabArchitecture(): void {
    this.useTwoTabArchitecture = true;
    console.log('✓ Two-tab architecture enabled');
  }

  /**
   * Test the two-tab architecture by performing basic operations
   * This method can be used to validate the architecture works correctly
   */
  async testTwoTabArchitecture(sessionId: string): Promise<{
    success: boolean;
    controlTabReady: boolean;
    targetTabReady: boolean;
    communicationWorking: boolean;
    error?: string;
  }> {
    try {
      console.log('→ Testing two-tab architecture...');

      // Setup two-tab session
      const twoTabResult = await this.setupTwoTabBrowserSession(sessionId);

      // Inject scripts
      const contexts = await this.injectTwoTabScripts(
        twoTabResult.wsEndpoint,
        'test-user',
        'test-platform',
        twoTabResult.controlTabSessionId,
        twoTabResult.targetTabSessionId,
        twoTabResult.controlTabId,
        twoTabResult.targetTabId,
      );

      // Test basic operations
      let controlTabReady = false;
      let targetTabReady = false;
      let communicationWorking = false;

      // Test control tab readiness
      try {
        await this.cdp!.Runtime.evaluate(
          {
            expression: 'typeof window.controlTabBrowserController !== "undefined"',
            returnByValue: true,
          },
          twoTabResult.controlTabSessionId,
        );
        controlTabReady = true;
        console.log('✓ Control tab ready');
      } catch (error) {
        console.error('✗ Control tab not ready:', error);
      }

      // Test target tab readiness
      try {
        await this.cdp!.Runtime.evaluate(
          {
            expression: 'typeof window.browserController !== "undefined"',
            returnByValue: true,
          },
          twoTabResult.targetTabSessionId,
        );
        targetTabReady = true;
        console.log('✓ Target tab ready');
      } catch (error) {
        console.error('✗ Target tab not ready:', error);
      }

      // Test communication by triggering a simple operation
      try {
        const result = await this.cdp!.Runtime.evaluate(
          {
            expression: `
              (async () => {
                try {
                  await window.browserController.setupBrowserMetrics({ width: 1024, height: 768 });
                  return { success: true };
                } catch (error) {
                  return { success: false, error: error.message };
                }
              })()
            `,
            awaitPromise: true,
            returnByValue: true,
          },
          twoTabResult.targetTabSessionId,
        );

        if (result.result?.value?.success) {
          communicationWorking = true;
          console.log('✓ Cross-tab communication working');
        } else {
          console.error('✗ Cross-tab communication failed:', result.result?.value?.error);
        }
      } catch (error) {
        console.error('✗ Communication test failed:', error);
      }

      const success = controlTabReady && targetTabReady && communicationWorking;

      console.log(`→ Two-tab architecture test ${success ? 'PASSED' : 'FAILED'}`);
      console.log(`  Control tab ready: ${controlTabReady}`);
      console.log(`  Target tab ready: ${targetTabReady}`);
      console.log(`  Communication working: ${communicationWorking}`);

      return {
        success,
        controlTabReady,
        targetTabReady,
        communicationWorking,
      };
    } catch (error) {
      console.error('✗ Two-tab architecture test failed:', error);
      return {
        success: false,
        controlTabReady: false,
        targetTabReady: false,
        communicationWorking: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  async run(event: WorkflowEvent<ConnectionsWorkflowParams>, step: WorkflowStep) {
    let browserWsEndpoint: string;

    if (this.useTwoTabArchitecture) {
      console.log('→ Using two-tab architecture');
      const twoTabResult = await this.setupTwoTabBrowserSession(event.payload.sessionId);
      browserWsEndpoint = twoTabResult.wsEndpoint;
      this.controlTabSessionId = twoTabResult.controlTabSessionId;
      this.controlTabId = twoTabResult.controlTabId;
      // targetSessionId and targetId are already set in setupTwoTabBrowserSession
    } else {
      console.log('→ Using single-tab architecture');
      browserWsEndpoint = await this.setupBrowserSession(event.payload.sessionId);
    }
    await step.do('setup viewport and device metrics', defaultWorkflowNoRetryConfig, async () => {
      await this.ensureViewportSettings();
      await setupInputFocusListener(this.cdp!, this.targetSessionId);
    });
    await step.do(
      'Initialize session and capture screenshot',
      defaultWorkflowNoRetryConfig,
      async () => this.navigateToLoginPage(event.payload.platformId),
    );
    // Step 2: screenshot
    const screenshot = await step.do(
      'Initialize session and capture screenshot',
      defaultWorkflowNoRetryConfig,
      async () => {
        return await this.captureScreenshot();
      },
    );
    let isCompleted = false;
    let captchaScreenUpdated = false;
    let latestScreenshot = screenshot;

    while (!isCompleted) {
      // Step 3: Generate form with OpenAI
      let formData = await step.do(
        'Generate form with OpenAI',
        defaultWorkflowRetryConfig,
        async () => {
          return await this.generateFormWithOpenAI(latestScreenshot);
        },
      );
      if (formData.pageType === 'loading') {
        console.log('→ Page is loading, waiting for completion and capturing new screenshot');

        const newScreenshot = await step.do(
          'Capture screenshot after loading',
          defaultWorkflowNoRetryConfig,
          async () => await this.captureScreenshot(),
        );

        latestScreenshot = newScreenshot;
        console.log('✓ Page loading completed, screenshot updated');
        continue;
      }
      if (formData.pageType === 'captcha') {
        //Save the screenshot to R2
        storeConnectionScreenshotToR2(
          this.env.SCREENSHOTS_INBOUND_BUCKET,
          event.payload.userId,
          event.payload.platformId,
          event.payload.sessionId,
          ConnectionWorkflowState.OnCaptcha,
          latestScreenshot,
        );

        const executionContextId = await step.do(
          'Inject script',
          defaultWorkflowRetryConfig,
          async () => {
            if (this.useTwoTabArchitecture) {
              const contexts = await this.injectTwoTabScripts(
                browserWsEndpoint,
                event.payload.userId,
                event.payload.platformId,
                this.controlTabSessionId!,
                this.targetSessionId!,
                this.controlTabId!,
                this.targetId!,
              );
              this.controlExecutionContextId = contexts.controlExecutionContextId;
              this.targetExecutionContextId = contexts.targetExecutionContextId;
              return contexts.targetExecutionContextId; // Return target context for compatibility
            } else {
              return await this.injectScriptsIntoAnIsolatedWorld(
                browserWsEndpoint,
                event.payload.userId,
                event.payload.platformId,
              );
            }
          },
        );
        let isCaptchaSolved = false;
        while (!isCaptchaSolved) {
          const viewport = { width: 1024, height: 768 };

          await step.do(
            'Send captcha detection to agent',
            defaultWorkflowNoRetryConfig,
            async () => {
              const { cdp, env } = this;

              if (!cdp) throw new Error('CDP client not initialized');

              const agentName = `${event.payload.userId}:${event.payload.platformId}`;
              const agentId = this.env.Connections.idFromName(agentName);
              const agent = this.env.Connections.get(agentId);
              if (captchaScreenUpdated) {
                // await agent.resumeInteractivity();
              } else {
                // await initRTCSteaming(
                //   { cdpSession: cdp },
                //   `${env.KAKU_WS_ENDPOINT}/agents/connections/${agentName}`,
                //   executionContextId,
                //   this.targetSessionId!,
                // );
              }
              // await agent.handleCaptchaDetected(executionContextId, viewport);
            },
          );

          console.log('Waiting for captcha solved notification');
          const captchaSolvedEvent = await step.waitForEvent<{
            differencePercentage: number;
            timestamp: string;
          }>('Await captcha solved', {
            type: 'captcha-solved',
            timeout: '15 minutes',
          });

          console.log(
            'Received captcha solved event',
            captchaSolvedEvent.payload.differencePercentage,
          );

          const newScreenshot = await this.captureScreenshot();

          await step.do('pause interactivity', defaultWorkflowRetryConfig, async () => {
            console.log('✓ interactivity paused');
            const agentName = `${event.payload.userId}:${event.payload.platformId}`;
            const agentId = this.env.Connections.idFromName(agentName);
            const agent = this.env.Connections.get(agentId);
            await agent.pauseInteractivity();
          });

          formData = await step.do(
            'Verify captcha status',
            defaultWorkflowRetryConfig,
            async () => {
              return await this.generateFormWithOpenAI(newScreenshot, true);
            },
          );
          if (formData.pageType !== 'captcha') {
            console.log('✓ Captcha solved, continuing with flow');
            isCaptchaSolved = true;

            const agentName = `${event.payload.userId}:${event.payload.platformId}`;
            const agentId = this.env.Connections.idFromName(agentName);
            const agent = this.env.Connections.get(agentId);
            await agent.handleCaptchaSolved();

            latestScreenshot = newScreenshot;
          } else {
            captchaScreenUpdated = true;
            console.log('Captcha still active, continuing to monitor');
          }
        }
      }

      // Step 4: Acknowledge extracted form
      await step.do('Acknowledge extracted form', defaultWorkflowNoRetryConfig, async () => {
        await this.acknowledgeExtractedForm(
          `${event.payload.userId}:${event.payload.platformId}`,
          formData,
        );
      });

      if (formData.pageType === 'authenticated') {
        console.log('✓ Workflow completed: user authenticated');
        isCompleted = true;
        if (this.cdp) {
          const browserDataAdapter = new CDPBrowserDataAdapter(this.cdp, this.targetSessionId);
          await this.browserStateService.updateBrowserState(
            browserDataAdapter,
            event.payload.userId,
            event.payload.platformId,
          );
        }
        await this.cleanupResources();

        //Save the screenshot to R2
        storeConnectionScreenshotToR2(
          this.env.SCREENSHOTS_INBOUND_BUCKET,
          event.payload.userId,
          event.payload.platformId,
          event.payload.sessionId,
          ConnectionWorkflowState.Authenticated,
          latestScreenshot,
        );

        break;
      }

      // Step 5: Wait for user form input
      const formSubmissionEvent = await step.waitForEvent<string>('Await user form input', {
        type: 'form-submission',
        timeout: '2 minutes',
      });
      console.log('✓ Received form submission event');

      // Step 6: Fill form in browser
      await step.do('Fill form in browser', defaultWorkflowNoRetryConfig, async () => {
        return await this.processFormSubmission(formSubmissionEvent.payload);
      });

      const screenshot = await step.do(
        'Take screenshot',
        defaultWorkflowNoRetryConfig,
        async () => {
          return await this.captureScreenshot();
        },
      );

      latestScreenshot = screenshot;

      //Save the screenshot to R2
      storeConnectionScreenshotToR2(
        this.env.SCREENSHOTS_INBOUND_BUCKET,
        event.payload.userId,
        event.payload.platformId,
        event.payload.sessionId,
        ConnectionWorkflowState.UserFormFilled,
        latestScreenshot,
      );
    }
  }

  private async setupBrowserSession(sessionId: string): Promise<string> {
    const browserSession = await this.browserService.getSession(sessionId);
    const wsEndpoint = browserSession.wsEndpoint;

    this.cdp = new CDP({ webSocketDebuggerUrl: wsEndpoint });

    try {
      await this.cdp.Target.setAutoAttach({
        autoAttach: true,
        flatten: true,
        waitForDebuggerOnStart: false,
      });

      // Set up event listener for target attachment
      const targetAttachedPromise = new Promise<string>((resolve) => {
        const handler = ({ params }: { params: any }) => {
          const { sessionId, targetInfo } = params;
          if (targetInfo.type === 'page') {
            this.targetSessionId = sessionId;
            this.targetId = targetInfo.targetId;
            this.cdp?.Target.removeEventListener('attachedToTarget', handler);
            resolve(sessionId);
          }
        };
        this.cdp?.Target.addEventListener('attachedToTarget', handler);
      });

      // Create a new target to trigger the attachedToTarget event
      await this.cdp.Target.createTarget({ url: 'about:blank' });

      // Wait for the target session to be attached
      await targetAttachedPromise;

      // Enable only required domains
      await this.cdp.Page.enable(undefined, this.targetSessionId);
      await this.cdp.Runtime.enable(undefined, this.targetSessionId);

      // Disable content security policy
      await this.cdp.Page.setBypassCSP({ enabled: true }, this.targetSessionId);

      console.log('✓ Browser session setup complete with targetSessionId:', this.targetSessionId);
    } catch (error) {
      console.error('Failed to set up browser session:', error);
      throw error;
    }

    return wsEndpoint;
  }

  /**
   * Setup two-tab browser architecture to prevent crashes during page reloads
   * Creates control tab first, then target tab with proper script injection
   */
  private async setupTwoTabBrowserSession(sessionId: string): Promise<{
    wsEndpoint: string;
    controlTabSessionId: string;
    targetTabSessionId: string;
    controlTabId: string;
    targetTabId: string;
  }> {
    const browserSession = await this.browserService.getSession(sessionId);
    const wsEndpoint = browserSession.wsEndpoint;

    this.cdp = new CDP({ webSocketDebuggerUrl: wsEndpoint });

    try {
      await this.cdp.Target.setAutoAttach({
        autoAttach: true,
        flatten: true,
        waitForDebuggerOnStart: false,
      });

      // Track both control and target tabs
      let controlTabSessionId: string | null = null;
      let targetTabSessionId: string | null = null;
      let controlTabId: string | null = null;
      let targetTabId: string | null = null;

      // Set up event listener for target attachment
      const bothTabsAttachedPromise = new Promise<void>((resolve) => {
        const handler = ({ params }: { params: any }) => {
          const { sessionId, targetInfo } = params;
          if (targetInfo.type === 'page') {
            if (!controlTabSessionId) {
              // First tab is control tab
              controlTabSessionId = sessionId;
              controlTabId = targetInfo.targetId;
              console.log('✓ Control tab attached:', controlTabId);
            } else if (!targetTabSessionId) {
              // Second tab is target tab
              targetTabSessionId = sessionId;
              targetTabId = targetInfo.targetId;
              this.targetSessionId = sessionId; // Keep for compatibility
              this.targetId = targetInfo.targetId; // Keep for compatibility
              console.log('✓ Target tab attached:', targetTabId);

              this.cdp?.Target.removeEventListener('attachedToTarget', handler);
              resolve();
            }
          }
        };
        this.cdp?.Target.addEventListener('attachedToTarget', handler);
      });

      // Create control tab first
      console.log('→ Creating control tab...');
      await this.cdp.Target.createTarget({ url: 'about:blank' });

      // Wait a moment for control tab to be ready
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Create target tab second
      console.log('→ Creating target tab...');
      await this.cdp.Target.createTarget({ url: 'about:blank' });

      // Wait for both tabs to be attached
      await bothTabsAttachedPromise;

      if (!controlTabSessionId || !targetTabSessionId || !controlTabId || !targetTabId) {
        throw new Error('Failed to create both control and target tabs');
      }

      // Enable domains for both tabs
      await Promise.all([
        // Control tab setup
        (async () => {
          await this.cdp!.Page.enable(undefined, controlTabSessionId);
          await this.cdp!.Runtime.enable(undefined, controlTabSessionId);
          await this.cdp!.Page.setBypassCSP({ enabled: true }, controlTabSessionId);
        })(),
        // Target tab setup
        (async () => {
          await this.cdp!.Page.enable(undefined, targetTabSessionId);
          await this.cdp!.Runtime.enable(undefined, targetTabSessionId);
          await this.cdp!.Page.setBypassCSP({ enabled: true }, targetTabSessionId);
        })(),
      ]);

      console.log('✓ Two-tab browser session setup complete');
      console.log(`  Control tab: ${controlTabId} (session: ${controlTabSessionId})`);
      console.log(`  Target tab: ${targetTabId} (session: ${targetTabSessionId})`);

      return {
        wsEndpoint,
        controlTabSessionId,
        targetTabSessionId,
        controlTabId,
        targetTabId,
      };
    } catch (error) {
      console.error('Failed to set up two-tab browser session:', error);
      throw error;
    }
  }

  private async navigateToLoginPage(platformId: PlatformTypes): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    console.log(`→ Navigating to ${platformId}`);
    const pageLoad = this.waitForPageLoad();

    await this.cdp.Page.navigate(
      {
        url: platformLoginLinks[platformId],
      },
      this.targetSessionId,
    );

    await pageLoad;
    console.log('✓ Page loaded');

    console.log('✓ Navigation completed and viewport configured');
  }

  private async waitForPageLoad(): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return Promise.resolve();
    await new Promise<void>((resolve) => {
      const handler = () => {
        this.cdp?.Page.removeEventListener('loadEventFired', handler);
        resolve();
      };
      this.cdp?.Page.addEventListener('loadEventFired', handler);
    });
  }

  /**
   * Ensures the viewport and device metrics are set to the correct dimensions
   * This should be called after navigation or any time we need to guarantee viewport consistency
   */
  private async ensureViewportSettings(): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    console.log('→ Ensuring viewport settings are correct (1024x768)');

    await this.cdp.Emulation.setDeviceMetricsOverride(
      {
        //store these to constants
        width: K_CUSTOM_VIEWPORT.width,
        height: K_CUSTOM_VIEWPORT.height,
        deviceScaleFactor: 1,
        mobile: false,
      },
      this.targetSessionId,
    );

    console.log('✓ Viewport settings confirmed');
  }

  private async captureScreenshot(): Promise<string> {
    if (!this.cdp || !this.targetSessionId) return '';

    console.log('→ Capturing screenshot');
    const screenshot = await this.cdp.Page.captureScreenshot(
      {
        format: 'webp',
        captureBeyondViewport: false,
      },
      this.targetSessionId,
    );

    const sizeKB = (screenshot.data.length * 0.75) / 1024;
    console.log(`✓ Screenshot captured: ${sizeKB.toFixed(2)} KB`);
    // console.log screenshot dimensions
    const dimensions = await this.cdp.Page.getLayoutMetrics(undefined, this.targetSessionId);
    console.log(
      `Screenshot dimensions: ${dimensions.contentSize.width} x ${dimensions.contentSize.height}`,
    );

    return screenshot.data;
  }

  private async injectScriptsIntoAnIsolatedWorld(
    browserWsEndpoint: string,
    userId: string,
    platformId: string,
  ): Promise<number> {
    if (!this.cdp || !this.targetSessionId)
      throw new Error('CDP client or target session not initialized');

    // Get the main frame ID first
    const frameTree = await this.cdp.Page.getFrameTree(undefined, this.targetSessionId);
    const mainFrameId = frameTree.frameTree.frame.id;

    const { executionContextId } = await this.cdp.Page.createIsolatedWorld(
      {
        frameId: mainFrameId,
        worldName: 'kaku-world',
        grantUniveralAccess: true,
      },
      this.targetSessionId,
    );
    console.log('Created isolated');
    const browserControllerUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'browser-controller.min.js',
    );

    // const screenCropperUrl = getHashedScriptUrl(
    //   this.env.KAKU_API_ENDPOINT,
    //   'screen-cropper.min.js',
    // );

    // const captchaDetectorUrl = getHashedScriptUrl(
    //   this.env.KAKU_API_ENDPOINT,
    //   'captcha-detector.min.js',
    // );

    // const screenshotComparisonUrl = getHashedScriptUrl(
    //   this.env.KAKU_API_ENDPOINT,
    //   'screenshot-comparison.min.js',
    // );

    // const captchaDetectorTfUrl = getHashedScriptUrl(
    //   this.env.KAKU_API_ENDPOINT,
    //   'captcha-detector-tf.min.js',
    // );

    // const tfModelBundleUrl = getHashedScriptUrl(
    //   this.env.KAKU_API_ENDPOINT,
    //   'tf-model-bundle.min.js',
    // );

    try {
      const cdp = this.cdp;
      const targetSessionId = this.targetSessionId;

      const layoutMetrics = await cdp.Page.getLayoutMetrics(undefined, targetSessionId);
      const viewport = {
        width: layoutMetrics.cssLayoutViewport.clientWidth,
        height: layoutMetrics.cssLayoutViewport.clientHeight,
      };
      // Group 1: Inject independent scripts in parallel
      console.log('→ Injecting core scripts in parallel...');
      await Promise.all([
        // (async () => {
        //   console.log('→ Injecting TensorFlow.js library');
        //   await injectTensorFlowJS(cdp, executionContextId, targetSessionId);
        // })(),
        // (async () => {
        //   console.log('→ Injecting screenshot comparison script');
        //   await injectScript(cdp, screenshotComparisonUrl, executionContextId, targetSessionId);
        // })(),
        (async () => {
          console.log('→ Injecting browser controller script');
          await injectScript(cdp, browserControllerUrl, executionContextId, targetSessionId);
        })(),
        // (async () => {
        //   console.log('→ Injecting screen cropper script');
        //   await injectScript(cdp, screenCropperUrl, executionContextId, targetSessionId);
        // })(),
        // (async () => {
        //   console.log('→ Injecting captcha detector script');
        //   await injectScript(cdp, captchaDetectorUrl, executionContextId, targetSessionId);
        // })(),
      ]);

      // Group 2: Initialize browser controller and inject TensorFlow-dependent scripts in parallel
      console.log(
        '→ Initializing browser controller and injecting TensorFlow-dependent scripts...',
      );
      await Promise.all([
        (async () => {
          console.log('→ Initialize browser controller');
          if (!this.targetId) {
            throw new Error('Target ID not available for browser controller initialization');
          }
          await initBrowserController(
            cdp,
            browserWsEndpoint,
            executionContextId,
            targetSessionId,
            this.targetId,
          );
        })(),
        // (async () => {
        //   console.log('→ Injecting TensorFlow captcha detector script');
        //   await injectScript(cdp, captchaDetectorTfUrl, executionContextId, targetSessionId);
        // })(),
      ]);

      // Group 3: Initialize screen cropper and inject tf-model-bundle in parallel
      console.log('→ Initializing screen cropper and injecting tf-model-bundle...');
      await Promise.all([
        (async () => {
          console.log('→ Initialize screen cropper');
          // await initScreenCropper(
          //   cdp,
          //   `${this.env.KAKU_WS_ENDPOINT}/agents/connections/${userId}:${platformId}`,
          //   executionContextId,
          //   { width: viewport.width, height: viewport.height },
          //   targetSessionId,
          // );
        })(),
        // (async () => {
        //   console.log('→ Injecting tf-model-bundle script');
        //   await injectScript(cdp, tfModelBundleUrl, executionContextId, targetSessionId);
        // })(),
      ]);

      // Group 4: Initialize TensorFlow detector (depends on all TensorFlow scripts)
      // console.log('→ Initializing TensorFlow detector.');
      // await initTensorFlowDetector(cdp, executionContextId, viewport, targetSessionId);
    } catch (error) {
      console.error('Error injecting script', error);
    }
    return executionContextId;
  }

  /**
   * Inject scripts for two-tab architecture
   * Control tab gets control-tab-browser-controller, target tab gets target-tab-proxy-controller
   */
  private async injectTwoTabScripts(
    browserWsEndpoint: string,
    userId: string,
    platformId: string,
    controlTabSessionId: string,
    targetTabSessionId: string,
    controlTabId: string,
    targetTabId: string,
  ): Promise<{ controlExecutionContextId: number; targetExecutionContextId: number }> {
    if (!this.cdp) throw new Error('CDP client not initialized');

    // Create isolated worlds for both tabs
    const [controlFrameTree, targetFrameTree] = await Promise.all([
      this.cdp.Page.getFrameTree(undefined, controlTabSessionId),
      this.cdp.Page.getFrameTree(undefined, targetTabSessionId),
    ]);

    const [controlContext, targetContext] = await Promise.all([
      this.cdp.Page.createIsolatedWorld(
        {
          frameId: controlFrameTree.frameTree.frame.id,
          worldName: 'kaku-control-world',
          grantUniveralAccess: true,
        },
        controlTabSessionId,
      ),
      this.cdp.Page.createIsolatedWorld(
        {
          frameId: targetFrameTree.frameTree.frame.id,
          worldName: 'kaku-target-world',
          grantUniveralAccess: true,
        },
        targetTabSessionId,
      ),
    ]);

    console.log('✓ Created isolated worlds for both tabs');

    // Get script URLs
    const controlTabBrowserControllerUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'control-tab-browser-controller.min.js',
    );
    const targetTabProxyControllerUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'target-tab-proxy-controller.min.js',
    );
    const messageProtocolUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'message-protocol.min.js',
    );
    const tabCommunicatorUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'tab-communicator.min.js',
    );

    try {
      const cdp = this.cdp;

      // Step 1: Inject shared dependencies in both tabs
      console.log('→ Injecting shared dependencies...');
      await Promise.all([
        // Control tab dependencies
        (async () => {
          await injectScript(
            cdp,
            messageProtocolUrl,
            controlContext.executionContextId,
            controlTabSessionId,
          );
          await injectScript(
            cdp,
            tabCommunicatorUrl,
            controlContext.executionContextId,
            controlTabSessionId,
          );
        })(),
        // Target tab dependencies
        (async () => {
          await injectScript(
            cdp,
            messageProtocolUrl,
            targetContext.executionContextId,
            targetTabSessionId,
          );
          await injectScript(
            cdp,
            tabCommunicatorUrl,
            targetContext.executionContextId,
            targetTabSessionId,
          );
        })(),
      ]);

      // Step 2: Inject tab-specific controllers
      console.log('→ Injecting tab-specific controllers...');
      await Promise.all([
        // Control tab controller
        (async () => {
          console.log('→ Injecting control tab browser controller');
          await injectScript(
            cdp,
            controlTabBrowserControllerUrl,
            controlContext.executionContextId,
            controlTabSessionId,
          );
        })(),
        // Target tab proxy controller
        (async () => {
          console.log('→ Injecting target tab proxy controller');
          await injectScript(
            cdp,
            targetTabProxyControllerUrl,
            targetContext.executionContextId,
            targetTabSessionId,
          );
        })(),
      ]);

      // Step 3: Initialize controllers with cross-tab communication
      console.log('→ Initializing two-tab browser controllers...');
      await Promise.all([
        // Initialize control tab controller
        (async () => {
          console.log('→ Initialize control tab browser controller');
          await initControlTabBrowserController(
            cdp,
            browserWsEndpoint,
            controlContext.executionContextId,
            controlTabSessionId,
            targetTabId,
          );
        })(),
        // Initialize target tab proxy controller
        (async () => {
          console.log('→ Initialize target tab proxy controller');
          await initTargetTabProxyController(
            cdp,
            browserWsEndpoint,
            targetContext.executionContextId,
            targetTabSessionId,
            targetTabId,
          );
        })(),
      ]);

      // Step 4: Inject additional scripts in target tab (screen-cropper, captcha detector, etc.)
      console.log('→ Injecting additional target tab scripts...');
      const screenCropperUrl = getHashedScriptUrl(
        this.env.KAKU_API_ENDPOINT,
        'screen-cropper.min.js',
      );
      const captchaDetectorUrl = getHashedScriptUrl(
        this.env.KAKU_API_ENDPOINT,
        'captcha-detector.min.js',
      );

      await Promise.all([
        // (async () => {
        //   console.log('→ Injecting screen cropper script');
        //   await injectScript(cdp, screenCropperUrl, targetContext.executionContextId, targetTabSessionId);
        // })(),
        // (async () => {
        //   console.log('→ Injecting captcha detector script');
        //   await injectScript(cdp, captchaDetectorUrl, targetContext.executionContextId, targetTabSessionId);
        // })(),
      ]);

      console.log('✓ Two-tab script injection complete');

      return {
        controlExecutionContextId: controlContext.executionContextId,
        targetExecutionContextId: targetContext.executionContextId,
      };
    } catch (error) {
      console.error('Error injecting two-tab scripts:', error);
      throw error;
    }
  }

  private async generateFormWithOpenAI(
    screenshot: string,
    bigDiff: boolean = false,
  ): Promise<PageStateResult> {
    return testLoginCaptcha1;
    const client = new OpenAI({
      apiKey: this.env.OPENAI_API_KEY,
      baseURL: this.env.AI_GATEWAY_URL,
    });
    const layoutMetrics = await this.cdp!.Page.getLayoutMetrics(undefined, this.targetSessionId);
    const viewPort = {
      width: layoutMetrics.cssLayoutViewport.clientWidth,
      height: layoutMetrics.cssLayoutViewport.clientHeight,
    };

    const response = await raceLLMCalls({
      client,
      screenshot,
      prompt: instructions,
      viewportWidth: viewPort?.width,
      viewportHeight: viewPort?.height,
      noOfConcurrentCalls: 2,
      enable: this.env.ENVIRONMENT !== 'local',
    });
    const end = Date.now();
    return extractFormJSON(response.result.output_text);
  }

  private async acknowledgeExtractedForm(connectionDOName: string, formData: any): Promise<void> {
    const agent = this.env.Connections.idFromName(connectionDOName);
    const stub = this.env.Connections.get(agent);

    console.log('→ Sending form to Durable Object');
    await stub.onFormStateChange({
      extractedData: formData,
    });
  }

  private async processFormSubmission(formSubmissionPayload: string): Promise<void> {
    try {
      console.log(
        '→ Starting form fill process with coordinate-based interaction',
        formSubmissionPayload,
      );
      const encryptionKey = await getEncryptionKey();
      const decrypted = await decryptData(formSubmissionPayload, encryptionKey);
      const actionsPayload = JSON.parse(decrypted as string) as FormSubmissionEventPayload;
      console.log(actionsPayload);
      // Sort actions by order, ensuring submit actions are last
      const sortedActions = actionsPayload.actions.sort((a, b) => a.order - b.order);

      // Process each action in order
      for (const action of sortedActions) {
        await this.executeAction(action);
      }

      console.log('→ Waiting for page to update after form submission');
      await this.waitForPageUpdateAfterSubmission();
    } catch (error) {
      console.error(`✖ Error filling form: ${(error as Error).message}`);
      throw error;
    }
  }

  private async executeAction(action: Action): Promise<void> {
    const { type, coordinates, name, value } = action;
    console.log(`→ Processing action "${type}" at (${coordinates.x}, ${coordinates.y})`);

    if (type === 'fill' && value) {
      await this.fillTextField(coordinates, name, value);
    } else if (type === 'click') {
      await this.clickAt(coordinates);
    }

    await sleep(100);
  }

  private async fillTextField(
    coordinates: { x: number; y: number },
    name: string,
    value: string,
  ): Promise<void> {
    try {
      console.log(`→ Focusing element for "${name}" at (${coordinates.x}, ${coordinates.y})`);

      // Click on the field
      await this.clickAt(coordinates);

      // Wait for focus
      await sleep(200);

      // Triple-click to select all text
      await this.tripleClickAt(coordinates);

      // Clear any existing text
      await this.pressBackspace();

      await sleep(200);
      console.log(`→ Typing "${value}" into field "${name}"`);

      // Type the value
      await this.typeText(value);

      console.log(`✓ Successfully entered text in "${name}"`);
    } catch (error) {
      throw Error(`Error clicking: ${(error as Error).message}`);
    }
  }

  private async clickAt(coordinates: { x: number; y: number }): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    try {
      // Mouse down
      await this.cdp.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: 1,
        },
        this.targetSessionId,
      );

      // Mouse up to complete click
      await this.cdp.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: 1,
        },
        this.targetSessionId,
      );
    } catch (error) {
      throw Error(`Error clicking: ${(error as Error).message}`);
    }
  }

  private async tripleClickAt(coordinates: { x: number; y: number }): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    for (let i = 0; i < 3; i++) {
      await this.cdp.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: i + 1,
        },
        this.targetSessionId,
      );
      await this.cdp.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: i + 1,
        },
        this.targetSessionId,
      );
      await sleep(50);
    }
  }

  private async pressBackspace(): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    await this.cdp.Input.dispatchKeyEvent(
      {
        type: 'keyDown',
        windowsVirtualKeyCode: 8,
        key: 'Backspace',
      },
      this.targetSessionId,
    );
    await this.cdp.Input.dispatchKeyEvent(
      {
        type: 'keyUp',
        key: 'Backspace',
      },
      this.targetSessionId,
    );
  }

  private async typeText(text: string): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    try {
      // Set the clipboard content
      await this.cdp.Input.insertText(
        {
          text: text,
        },
        this.targetSessionId,
      );

      console.log(
        `✓ Successfully pasted text: "${text.substring(0, 10)}${text.length > 10 ? '...' : ''}"`,
      );
    } catch (error) {
      throw Error(`✖ Error pasting text: ${(error as Error).message}`);
    }
  }

  private async waitForPageUpdateAfterSubmission(): Promise<void> {
    if (!this.cdp) return;

    const pageLoad = this.waitForPageLoad();
    console.log('→ Waiting for page load');
    await withTimeout(pageLoad, 10000);
    console.log('✓ Page reloaded after submission');
  }

  private async cleanupResources(): Promise<void> {
    if (this.cdp) {
      this.cdp.connection.close();
      this.cdp = undefined;
    }
  }
}

function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<unknown> {
  return Promise.race([
    promise,
    new Promise((resolve) => setTimeout(() => resolve(true), timeoutMs)),
  ]);
}

function sleep(timeInMillis: number): Promise<void> {
  return new Promise((r) => setTimeout(r, timeInMillis));
}

// Version 136.0.7103.92 (Official Build) (64-bit)
