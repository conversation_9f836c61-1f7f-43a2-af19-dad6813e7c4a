/**
 * Message Protocol for Two-Tab Browser Controller Architecture
 * 
 * This protocol defines the communication structure between:
 * - Control Tab: Manages CDP operations and browser automation
 * - Target Tab: Contains the actual page being automated
 */

export interface BaseMessage {
  id: string;
  type: string;
  timestamp: number;
}

export interface RequestMessage extends BaseMessage {
  type: 'REQUEST';
  method: string;
  params?: any;
  sessionId?: string;
}

export interface ResponseMessage extends BaseMessage {
  type: 'RESPONSE';
  requestId: string;
  success: boolean;
  result?: any;
  error?: {
    message: string;
    code?: string;
    stack?: string;
  };
}

export interface EventMessage extends BaseMessage {
  type: 'EVENT';
  eventType: string;
  data: any;
  source: 'control' | 'target';
}

export interface HandshakeMessage extends BaseMessage {
  type: 'HANDSHAKE';
  role: 'control' | 'target';
  capabilities?: string[];
}

export interface HeartbeatMessage extends BaseMessage {
  type: 'HEARTBEAT';
  source: 'control' | 'target';
}

export type Message = RequestMessage | ResponseMessage | EventMessage | HandshakeMessage | HeartbeatMessage;

/**
 * CDP Method Categories for the Browser Controller
 */
export interface CDPMethods {
  // Mouse Events
  'dispatchMouseMove': { x: number; y: number };
  'dispatchMouseDown': { x: number; y: number; button?: string };
  'dispatchMouseUp': { x: number; y: number; button?: string };
  'dispatchMouseClick': { x: number; y: number; button?: string };
  
  // Keyboard Events
  'dispatchKeyEvent': { type: string; key: string; code?: string; windowsVirtualKeyCode?: number };
  'insertText': { text: string };
  
  // Browser Metrics
  'setupBrowserMetrics': { width: number; height: number };
  
  // Screenshots
  'takeScreenshot': { quality?: number; format?: string };
  
  // Frame Generation
  'requestNewFrame': {};
  'triggerMouseMovement': {};
  
  // Input Handling
  'handleInputEvent': { type: string; x?: number; y?: number; text?: string; button?: number };
}

/**
 * Event Types that can be forwarded between tabs
 */
export interface EventTypes {
  // Input Events from Target Tab
  'input-event': {
    type: 'mousedown' | 'mouseup' | 'mousemove' | 'click' | 'char-input';
    x?: number;
    y?: number;
    button?: number;
    text?: string;
    source?: string;
  };
  
  // Screen Cropper Events
  'screen-event': {
    type: 'crop-update' | 'frame-request' | 'streaming-status';
    data: any;
  };
  
  // Captcha Detection Events
  'captcha-event': {
    type: 'detection-trigger' | 'comparison-result' | 'ui-settled';
    data: any;
  };
  
  // Navigation Events
  'navigation-event': {
    type: 'page-load' | 'navigation-start' | 'navigation-complete';
    url?: string;
    timestamp: number;
  };
  
  // Error Events
  'error-event': {
    type: 'cdp-error' | 'connection-error' | 'script-error';
    error: string;
    details?: any;
  };
}

/**
 * Message Builder Utilities
 */
export class MessageBuilder {
  private static generateId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  static createRequest<T extends keyof CDPMethods>(
    method: T,
    params: CDPMethods[T],
    sessionId?: string
  ): RequestMessage {
    return {
      id: this.generateId(),
      type: 'REQUEST',
      method,
      params,
      sessionId,
      timestamp: Date.now(),
    };
  }

  static createResponse(
    requestId: string,
    success: boolean,
    result?: any,
    error?: { message: string; code?: string; stack?: string }
  ): ResponseMessage {
    return {
      id: this.generateId(),
      type: 'RESPONSE',
      requestId,
      success,
      result,
      error,
      timestamp: Date.now(),
    };
  }

  static createEvent<T extends keyof EventTypes>(
    eventType: T,
    data: EventTypes[T],
    source: 'control' | 'target'
  ): EventMessage {
    return {
      id: this.generateId(),
      type: 'EVENT',
      eventType,
      data,
      source,
      timestamp: Date.now(),
    };
  }

  static createHandshake(
    role: 'control' | 'target',
    capabilities?: string[]
  ): HandshakeMessage {
    return {
      id: this.generateId(),
      type: 'HANDSHAKE',
      role,
      capabilities,
      timestamp: Date.now(),
    };
  }

  static createHeartbeat(source: 'control' | 'target'): HeartbeatMessage {
    return {
      id: this.generateId(),
      type: 'HEARTBEAT',
      source,
      timestamp: Date.now(),
    };
  }
}

/**
 * Message Validation Utilities
 */
export class MessageValidator {
  static isValidMessage(data: any): data is Message {
    return (
      data &&
      typeof data === 'object' &&
      typeof data.id === 'string' &&
      typeof data.type === 'string' &&
      typeof data.timestamp === 'number'
    );
  }

  static isRequest(message: Message): message is RequestMessage {
    return message.type === 'REQUEST' && 'method' in message;
  }

  static isResponse(message: Message): message is ResponseMessage {
    return message.type === 'RESPONSE' && 'requestId' in message && 'success' in message;
  }

  static isEvent(message: Message): message is EventMessage {
    return message.type === 'EVENT' && 'eventType' in message && 'data' in message;
  }

  static isHandshake(message: Message): message is HandshakeMessage {
    return message.type === 'HANDSHAKE' && 'role' in message;
  }

  static isHeartbeat(message: Message): message is HeartbeatMessage {
    return message.type === 'HEARTBEAT' && 'source' in message;
  }
}

/**
 * Connection States for Tab Communication
 */
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
  RECONNECTING = 'reconnecting',
}

/**
 * Configuration for Message Passing
 */
export interface MessageConfig {
  requestTimeout: number;
  heartbeatInterval: number;
  maxRetries: number;
  retryDelay: number;
  enableHeartbeat: boolean;
  enableLogging: boolean;
}

export const DEFAULT_MESSAGE_CONFIG: MessageConfig = {
  requestTimeout: 10000, // 10 seconds
  heartbeatInterval: 30000, // 30 seconds
  maxRetries: 3,
  retryDelay: 1000, // 1 second
  enableHeartbeat: true,
  enableLogging: true,
};
