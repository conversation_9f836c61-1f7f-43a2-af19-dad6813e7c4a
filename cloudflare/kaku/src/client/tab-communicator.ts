/**
 * Cross-Tab Communication Layer
 *
 * Handles robust message passing between control tab and target tab
 * with promise resolution, error handling, and connection recovery.
 */

import {
  Message,
  RequestMessage,
  ResponseMessage,
  EventMessage,
  HandshakeMessage,
  HeartbeatMessage,
  MessageBuilder,
  MessageValidator,
  ConnectionState,
  MessageConfig,
  DEFAULT_MESSAGE_CONFIG,
  CDPMethods,
  EventTypes,
} from './message-protocol';

export interface TabCommunicatorOptions {
  role: 'control' | 'target';
  targetOrigin?: string;
  config?: Partial<MessageConfig>;
  onConnectionStateChange?: (state: ConnectionState) => void;
  onError?: (error: Error) => void;
}

export class TabCommunicator {
  private role: 'control' | 'target';
  private config: MessageConfig;
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private targetWindow: Window | null = null;
  private targetOrigin: string;

  // Promise management for request/response pattern
  private pendingRequests = new Map<
    string,
    {
      resolve: (value: any) => void;
      reject: (error: Error) => void;
      timeout: NodeJS.Timeout;
      retryCount: number;
    }
  >();

  // Event listeners
  private eventListeners = new Map<string, Set<(data: any) => void>>();
  private messageHandler: ((event: MessageEvent) => void) | null = null;

  // Heartbeat management
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private lastHeartbeatReceived = 0;

  // Callbacks
  private onConnectionStateChange?: (state: ConnectionState) => void;
  private onError?: (error: Error) => void;

  constructor(options: TabCommunicatorOptions) {
    this.role = options.role;
    this.targetOrigin = options.targetOrigin || '*';
    this.config = { ...DEFAULT_MESSAGE_CONFIG, ...options.config };
    this.onConnectionStateChange = options.onConnectionStateChange;
    this.onError = options.onError;

    this.setupMessageListener();
    this.setupVisibilityHandling();
    this.startConnectionMonitoring();
    this.log('TabCommunicator initialized for role:', this.role);
  }

  /**
   * Initialize connection with the other tab
   */
  async connect(targetWindow?: Window): Promise<void> {
    if (targetWindow) {
      this.targetWindow = targetWindow;
    }

    this.setConnectionState(ConnectionState.CONNECTING);

    try {
      // Send handshake
      const handshake = MessageBuilder.createHandshake(this.role, this.getCapabilities());
      await this.sendMessage(handshake);

      // Start heartbeat if enabled
      if (this.config.enableHeartbeat) {
        this.startHeartbeat();
      }

      this.setConnectionState(ConnectionState.CONNECTED);
      this.log('Connection established successfully');
    } catch (error) {
      this.setConnectionState(ConnectionState.ERROR);
      this.handleError(new Error(`Failed to connect: ${error.message}`));
      throw error;
    }
  }

  /**
   * Send a CDP method request to the control tab
   */
  async sendRequest<T extends keyof CDPMethods>(
    method: T,
    params: CDPMethods[T],
    sessionId?: string,
  ): Promise<any> {
    if (this.connectionState !== ConnectionState.CONNECTED) {
      throw new Error(`Cannot send request: connection state is ${this.connectionState}`);
    }

    const request = MessageBuilder.createRequest(method, params, sessionId);

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(request.id);
        reject(new Error(`Request timeout for method: ${method}`));
      }, this.config.requestTimeout);

      this.pendingRequests.set(request.id, {
        resolve,
        reject,
        timeout,
        retryCount: 0,
      });

      this.sendMessage(request).catch((error) => {
        this.pendingRequests.delete(request.id);
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  /**
   * Send an event to the other tab
   */
  async sendEvent<T extends keyof EventTypes>(eventType: T, data: EventTypes[T]): Promise<void> {
    const event = MessageBuilder.createEvent(eventType, data, this.role);
    await this.sendMessage(event);
  }

  /**
   * Listen for events from the other tab
   */
  addEventListener<T extends keyof EventTypes>(
    eventType: T,
    listener: (data: EventTypes[T]) => void,
  ): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }
    this.eventListeners.get(eventType)!.add(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener<T extends keyof EventTypes>(
    eventType: T,
    listener: (data: EventTypes[T]) => void,
  ): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  /**
   * Disconnect and cleanup
   */
  disconnect(): void {
    this.setConnectionState(ConnectionState.DISCONNECTED);

    // Clear pending requests
    this.pendingRequests.forEach(({ reject, timeout }) => {
      clearTimeout(timeout);
      reject(new Error('Connection closed'));
    });
    this.pendingRequests.clear();

    // Stop heartbeat
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    // Remove message listener
    if (this.messageHandler) {
      window.removeEventListener('message', this.messageHandler);
      this.messageHandler = null;
    }

    this.log('Disconnected and cleaned up');
  }

  /**
   * Get current connection state
   */
  getConnectionState(): ConnectionState {
    return this.connectionState;
  }

  /**
   * Set target window for communication
   */
  setTargetWindow(window: Window): void {
    this.targetWindow = window;
  }

  private setupMessageListener(): void {
    this.messageHandler = (event: MessageEvent) => {
      // Validate origin if specified
      if (this.targetOrigin !== '*' && event.origin !== this.targetOrigin) {
        return;
      }

      try {
        const message = event.data;

        if (!MessageValidator.isValidMessage(message)) {
          return;
        }

        this.handleMessage(message);
      } catch (error) {
        this.handleError(new Error(`Failed to handle message: ${error.message}`));
      }
    };

    window.addEventListener('message', this.messageHandler);
  }

  private async handleMessage(message: Message): Promise<void> {
    this.log('Received message:', message.type, message.id);

    if (MessageValidator.isResponse(message)) {
      this.handleResponse(message);
    } else if (MessageValidator.isRequest(message)) {
      await this.handleRequest(message);
    } else if (MessageValidator.isEvent(message)) {
      this.handleEvent(message);
    } else if (MessageValidator.isHandshake(message)) {
      this.handleHandshake(message);
    } else if (MessageValidator.isHeartbeat(message)) {
      this.handleHeartbeat(message);
    }
  }

  private handleResponse(response: ResponseMessage): void {
    const pending = this.pendingRequests.get(response.requestId);
    if (!pending) {
      this.log('Received response for unknown request:', response.requestId);
      return;
    }

    this.pendingRequests.delete(response.requestId);
    clearTimeout(pending.timeout);

    if (response.success) {
      pending.resolve(response.result);
    } else {
      const error = new Error(response.error?.message || 'Request failed');
      if (response.error?.code) {
        (error as any).code = response.error.code;
      }
      pending.reject(error);
    }
  }

  private async handleRequest(request: RequestMessage): Promise<void> {
    // This will be implemented by subclasses (ControlTabCommunicator, TargetTabCommunicator)
    // to handle CDP method execution
    this.log('Received request that should be handled by subclass:', request.method);
  }

  private handleEvent(event: EventMessage): void {
    const listeners = this.eventListeners.get(event.eventType);
    if (listeners) {
      listeners.forEach((listener) => {
        try {
          listener(event.data);
        } catch (error) {
          this.handleError(new Error(`Event listener error: ${error.message}`));
        }
      });
    }
  }

  private handleHandshake(handshake: HandshakeMessage): void {
    this.log('Received handshake from:', handshake.role);

    if (handshake.role !== this.role) {
      this.setConnectionState(ConnectionState.CONNECTED);
      this.lastHeartbeatReceived = Date.now();
    }
  }

  private handleHeartbeat(heartbeat: HeartbeatMessage): void {
    this.lastHeartbeatReceived = Date.now();

    // Send heartbeat response if we're the target
    if (this.role === 'target' && heartbeat.source === 'control') {
      const response = MessageBuilder.createHeartbeat('target');
      this.sendMessage(response).catch((error) => {
        this.handleError(new Error(`Failed to send heartbeat response: ${error.message}`));
      });
    }
  }

  private async sendMessage(message: Message): Promise<void> {
    if (!this.targetWindow) {
      throw new Error('Target window not set');
    }

    try {
      this.targetWindow.postMessage(message, this.targetOrigin);
      this.log('Sent message:', message.type, message.id);
    } catch (error) {
      throw new Error(`Failed to send message: ${error.message}`);
    }
  }

  private startHeartbeat(): void {
    if (this.role !== 'control') return; // Only control tab sends heartbeats

    this.heartbeatInterval = setInterval(() => {
      const heartbeat = MessageBuilder.createHeartbeat('control');
      this.sendMessage(heartbeat).catch((error) => {
        this.handleError(new Error(`Failed to send heartbeat: ${error.message}`));
      });

      // Check if we've received a recent heartbeat response
      const timeSinceLastHeartbeat = Date.now() - this.lastHeartbeatReceived;
      if (timeSinceLastHeartbeat > this.config.heartbeatInterval * 2) {
        this.setConnectionState(ConnectionState.ERROR);
        this.handleError(new Error('Heartbeat timeout - connection may be lost'));
      }
    }, this.config.heartbeatInterval);
  }

  private setConnectionState(state: ConnectionState): void {
    if (this.connectionState !== state) {
      this.connectionState = state;
      this.log('Connection state changed to:', state);
      this.onConnectionStateChange?.(state);
    }
  }

  private handleError(error: Error): void {
    this.log('Error:', error.message);
    this.onError?.(error);
  }

  private getCapabilities(): string[] {
    // Return capabilities based on role
    if (this.role === 'control') {
      return ['cdp-operations', 'browser-automation', 'screenshot-capture'];
    } else {
      return ['event-forwarding', 'input-detection', 'screen-streaming'];
    }
  }

  private log(...args: any[]): void {
    if (this.config.enableLogging) {
      console.log(`[TabCommunicator:${this.role}]`, ...args);
    }
  }

  /**
   * Attempt to reconnect after connection failure
   */
  private async attemptReconnection(): Promise<void> {
    if (this.connectionState === ConnectionState.RECONNECTING) {
      return; // Already attempting reconnection
    }

    this.setConnectionState(ConnectionState.RECONNECTING);
    this.log('Attempting to reconnect...');

    let retryCount = 0;
    while (
      retryCount < this.config.maxRetries &&
      this.connectionState === ConnectionState.RECONNECTING
    ) {
      try {
        await new Promise((resolve) =>
          setTimeout(resolve, this.config.retryDelay * (retryCount + 1)),
        );

        // Try to re-establish connection
        await this.connect();
        this.log('Reconnection successful');
        return;
      } catch (error) {
        retryCount++;
        this.log(`Reconnection attempt ${retryCount} failed:`, error.message);

        if (retryCount >= this.config.maxRetries) {
          this.setConnectionState(ConnectionState.ERROR);
          this.handleError(new Error('Max reconnection attempts reached'));
          break;
        }
      }
    }
  }

  /**
   * Handle tab visibility changes and connection recovery
   */
  private setupVisibilityHandling(): void {
    document.addEventListener('visibilitychange', () => {
      if (
        document.visibilityState === 'visible' &&
        this.connectionState === ConnectionState.ERROR
      ) {
        this.log('Tab became visible, attempting recovery...');
        this.attemptReconnection();
      }
    });

    // Handle page unload to cleanup connections
    window.addEventListener('beforeunload', () => {
      this.disconnect();
    });
  }

  /**
   * Monitor connection health and trigger recovery if needed
   */
  private startConnectionMonitoring(): void {
    if (this.role !== 'control') return; // Only control tab monitors

    setInterval(() => {
      if (this.connectionState === ConnectionState.CONNECTED) {
        const timeSinceLastHeartbeat = Date.now() - this.lastHeartbeatReceived;

        if (timeSinceLastHeartbeat > this.config.heartbeatInterval * 3) {
          this.log('Connection appears to be lost, attempting recovery...');
          this.setConnectionState(ConnectionState.ERROR);
          this.attemptReconnection();
        }
      }
    }, this.config.heartbeatInterval);
  }
}
