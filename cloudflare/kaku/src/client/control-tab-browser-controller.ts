/**
 * Control Tab Browser Controller
 * 
 * Runs in the control tab and manages all CDP operations.
 * Communicates with target tab via postMessage for event forwarding.
 * This architecture prevents browser crashes during page reloads.
 */

import { CDP } from '../browser/simple-cdp';
import { TabCommunicator } from './tab-communicator';
import { MessageBuilder, CDPMethods, EventTypes, ConnectionState } from './message-protocol';
import type { Protocol } from 'devtools-protocol';

interface ControlTabOptions {
  browserWsEndpoint: string;
  targetId: string;
  targetWindow?: Window;
  onError?: (error: Error) => void;
  onConnectionStateChange?: (state: ConnectionState) => void;
}

class ControlTabBrowserController {
  private cdpClient: CDP | null = null;
  private sessionId: string | null = null;
  private targetId: string;
  private browserWsEndpoint: string;
  private communicator: TabCommunicator;
  private isMouseDown = false;
  
  // Event handlers
  private onError?: (error: Error) => void;
  private onConnectionStateChange?: (state: ConnectionState) => void;

  constructor(options: ControlTabOptions) {
    this.browserWsEndpoint = options.browserWsEndpoint;
    this.targetId = options.targetId;
    this.onError = options.onError;
    this.onConnectionStateChange = options.onConnectionStateChange;

    // Initialize tab communicator
    this.communicator = new TabCommunicator({
      role: 'control',
      onConnectionStateChange: this.handleConnectionStateChange.bind(this),
      onError: this.handleCommunicatorError.bind(this),
    });

    if (options.targetWindow) {
      this.communicator.setTargetWindow(options.targetWindow);
    }

    this.setupRequestHandlers();
    this.log('Control tab browser controller initialized');
  }

  /**
   * Initialize CDP connection and establish communication with target tab
   */
  async init(): Promise<void> {
    try {
      await this.connectToCDPAndAttachToTarget();
      await this.communicator.connect();
      this.log('✓ Control tab browser controller ready');
    } catch (error) {
      this.error('Failed to initialize control tab browser controller:', error);
      throw error;
    }
  }

  /**
   * Set target window for communication
   */
  setTargetWindow(window: Window): void {
    this.communicator.setTargetWindow(window);
  }

  /**
   * Cleanup and disconnect
   */
  async cleanup(): Promise<void> {
    this.communicator.disconnect();
    
    if (this.cdpClient) {
      this.cdpClient.reset();
      this.cdpClient = null;
    }
    
    this.sessionId = null;
    this.log('Control tab browser controller cleaned up');
  }

  private async connectToCDPAndAttachToTarget(): Promise<void> {
    try {
      // Create CDP connection
      this.cdpClient = new CDP({ webSocketDebuggerUrl: this.browserWsEndpoint });

      this.log('Attaching to target:', this.targetId);

      // Attach to the existing page target
      const { sessionId } = await this.cdpClient.Target.attachToTarget({
        targetId: this.targetId,
        flatten: true,
      });

      this.sessionId = sessionId;

      await this.cdpClient.Page.enable(undefined, this.sessionId);
      await this.cdpClient.Runtime.enable(undefined, this.sessionId);

      this.log('✓ CDP attached to target', this.targetId, 'with sessionId:', this.sessionId);
    } catch (err) {
      this.error('Failed to connect to CDP and attach to target:', err);
      throw err;
    }
  }

  private setupRequestHandlers(): void {
    // Override the handleRequest method in the communicator
    const originalHandleRequest = (this.communicator as any).handleRequest;
    (this.communicator as any).handleRequest = async (request: any) => {
      try {
        const result = await this.executeMethod(request.method, request.params, request.sessionId);
        const response = MessageBuilder.createResponse(request.id, true, result);
        await (this.communicator as any).sendMessage(response);
      } catch (error) {
        const response = MessageBuilder.createResponse(
          request.id,
          false,
          undefined,
          {
            message: error.message,
            code: (error as any).code,
            stack: error.stack,
          }
        );
        await (this.communicator as any).sendMessage(response);
      }
    };
  }

  private async executeMethod(method: string, params: any, sessionId?: string): Promise<any> {
    if (!this.cdpClient || !this.sessionId) {
      throw new Error('CDP client not initialized');
    }

    const effectiveSessionId = sessionId || this.sessionId;

    switch (method) {
      case 'setupBrowserMetrics':
        return await this.setupBrowserMetrics(params);
      
      case 'dispatchMouseMove':
        return await this.dispatchMouseMove(params.x, params.y);
      
      case 'dispatchMouseDown':
        return await this.dispatchMouseDown(params.x, params.y, params.button);
      
      case 'dispatchMouseUp':
        return await this.dispatchMouseUp(params.x, params.y, params.button);
      
      case 'dispatchMouseClick':
        return await this.dispatchMouseClick(params.x, params.y, params.button);
      
      case 'dispatchKeyEvent':
        return await this.dispatchKeyEvent(params);
      
      case 'insertText':
        return await this.insertText(params.text);
      
      case 'takeScreenshot':
        return await this.takeScreenshot(params.quality, params.format);
      
      case 'requestNewFrame':
        return await this.requestNewFrame();
      
      case 'triggerMouseMovement':
        return await this.triggerMouseMovement();
      
      case 'handleInputEvent':
        return await this.handleInputEvent(params);
      
      default:
        throw new Error(`Unknown method: ${method}`);
    }
  }

  private async setupBrowserMetrics(viewPort: { width: number; height: number }): Promise<void> {
    if (!this.cdpClient || !this.sessionId) {
      throw new Error('CDP client not initialized');
    }

    await this.cdpClient.Emulation.setDeviceMetricsOverride(
      {
        width: viewPort.width,
        height: viewPort.height,
        deviceScaleFactor: 1,
        mobile: false,
      },
      this.sessionId,
    );

    await this.cdpClient.Emulation.setVisibleSize(
      {
        width: viewPort.width,
        height: viewPort.height,
      },
      this.sessionId,
    );

    this.log(`✓ Browser metrics set to ${viewPort.width}x${viewPort.height}`);
  }

  private async dispatchMouseMove(x: number, y: number): Promise<void> {
    if (!this.cdpClient || !this.sessionId) {
      throw new Error('CDP client not initialized');
    }

    await this.cdpClient.Input.dispatchMouseEvent(
      {
        type: 'mouseMoved',
        x: x,
        y: y,
        buttons: this.isMouseDown ? 1 : 0,
      },
      this.sessionId,
    );
  }

  private async dispatchMouseDown(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<void> {
    if (!this.cdpClient || !this.sessionId) {
      throw new Error('CDP client not initialized');
    }

    await this.cdpClient.Input.dispatchMouseEvent(
      {
        type: 'mousePressed',
        x: x,
        y: y,
        button: button,
        clickCount: 1,
        buttons: button === 'left' ? 1 : 2,
      },
      this.sessionId,
    );
    this.isMouseDown = true;
    this.log(`Mouse down at (${x}, ${y})`);
  }

  private async dispatchMouseUp(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<void> {
    if (!this.cdpClient || !this.sessionId) {
      throw new Error('CDP client not initialized');
    }

    await this.cdpClient.Input.dispatchMouseEvent(
      {
        type: 'mouseReleased',
        x: x,
        y: y,
        button: button,
        clickCount: 1,
        buttons: 0,
      },
      this.sessionId,
    );
    this.isMouseDown = false;
    this.log(`Mouse up at (${x}, ${y})`);
  }

  private async dispatchMouseClick(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<void> {
    await this.dispatchMouseDown(x, y, button);
    await new Promise(resolve => setTimeout(resolve, 50)); // Small delay
    await this.dispatchMouseUp(x, y, button);
  }

  private async dispatchKeyEvent(params: {
    type: string;
    key: string;
    code?: string;
    windowsVirtualKeyCode?: number;
  }): Promise<void> {
    if (!this.cdpClient || !this.sessionId) {
      throw new Error('CDP client not initialized');
    }

    await this.cdpClient.Input.dispatchKeyEvent(params as any, this.sessionId);
  }

  private async insertText(text: string): Promise<void> {
    if (!this.cdpClient || !this.sessionId) {
      throw new Error('CDP client not initialized');
    }

    await this.cdpClient.Input.insertText({ text }, this.sessionId);
  }

  private async takeScreenshot(quality?: number, format?: string): Promise<string> {
    if (!this.cdpClient || !this.sessionId) {
      throw new Error('CDP client not initialized');
    }

    const result = await this.cdpClient.Page.captureScreenshot(
      {
        format: (format as any) || 'png',
        quality: quality || 80,
      },
      this.sessionId,
    );

    return result.data;
  }

  private async requestNewFrame(): Promise<void> {
    // Trigger frame generation using opacity technique
    if (!this.cdpClient || !this.sessionId) {
      throw new Error('CDP client not initialized');
    }

    await this.cdpClient.Runtime.evaluate(
      {
        expression: `
          (() => {
            try {
              const elements = document.querySelectorAll('*');
              if (elements.length > 0) {
                const element = elements[Math.floor(Math.random() * elements.length)];
                const originalOpacity = element.style.opacity;
                element.style.opacity = '0.999';
                setTimeout(() => {
                  element.style.opacity = originalOpacity;
                }, 1);
              }
              return { success: true, timestamp: Date.now() };
            } catch (e) {
              return { success: false, error: e.message, timestamp: Date.now() };
            }
          })()
        `,
        awaitPromise: true,
        returnByValue: true,
      },
      this.sessionId,
    );
  }

  private async triggerMouseMovement(): Promise<void> {
    // Generate small mouse movements to trigger frame updates
    const centerX = 400;
    const centerY = 300;
    
    for (let i = 0; i < 3; i++) {
      const offsetX = Math.random() * 4 - 2; // -2 to 2 pixels
      const offsetY = Math.random() * 4 - 2;
      await this.dispatchMouseMove(centerX + offsetX, centerY + offsetY);
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }

  private async handleInputEvent(data: any): Promise<void> {
    switch (data.type) {
      case 'mousedown':
        await this.dispatchMouseDown(data.x, data.y, data.button === 0 ? 'left' : 'right');
        break;
      case 'mouseup':
        await this.dispatchMouseUp(data.x, data.y, data.button === 0 ? 'left' : 'right');
        break;
      case 'mousemove':
        await this.dispatchMouseMove(data.x, data.y);
        break;
      case 'click':
        await this.dispatchMouseClick(data.x, data.y, data.button === 0 ? 'left' : 'right');
        break;
      case 'char-input':
        await this.insertText(data.text);
        break;
      default:
        this.log('Unknown input event type:', data.type);
    }
  }

  private handleConnectionStateChange(state: ConnectionState): void {
    this.log('Connection state changed to:', state);
    this.onConnectionStateChange?.(state);
  }

  private handleCommunicatorError(error: Error): void {
    this.error('Communicator error:', error);
    this.onError?.(error);
  }

  private log(...args: any[]): void {
    console.log('[ControlTabBrowserController]', ...args);
  }

  private error(...args: any[]): void {
    console.error('[ControlTabBrowserController]', ...args);
  }
}

// Global initialization function
(async function initControlTabBrowserController() {
  let controller: ControlTabBrowserController | null = null;

  async function init(
    browserWsEndpoint: string,
    targetId: string,
    targetWindow?: Window
  ): Promise<void> {
    if (controller) {
      console.log('Control tab browser controller already initialized');
      return;
    }

    controller = new ControlTabBrowserController({
      browserWsEndpoint,
      targetId,
      targetWindow,
      onError: (error) => console.error('Control tab error:', error),
      onConnectionStateChange: (state) => console.log('Connection state:', state),
    });

    await controller.init();
  }

  function setTargetWindow(window: Window): void {
    if (controller) {
      controller.setTargetWindow(window);
    }
  }

  async function cleanup(): Promise<void> {
    if (controller) {
      await controller.cleanup();
      controller = null;
    }
  }

  // Expose global API
  (globalThis as any).controlTabBrowserController = {
    init,
    setTargetWindow,
    cleanup,
  };
})();
