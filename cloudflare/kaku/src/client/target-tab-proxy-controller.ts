/**
 * Target Tab Proxy Controller
 *
 * Lightweight proxy that runs in the target tab and forwards all browser controller
 * operations to the control tab via postMessage. Maintains the same API surface
 * as the original browser-controller for compatibility with existing scripts.
 */

import { TabCommunicator } from './tab-communicator';
import { ConnectionState, CDPMethods } from './message-protocol';

interface ProxyControllerOptions {
  controlWindow?: Window;
  onError?: (error: Error) => void;
  onConnectionStateChange?: (state: ConnectionState) => void;
}

class TargetTabProxyController {
  private communicator: TabCommunicator;
  private isInitialized = false;

  // Event handlers
  private onError?: (error: Error) => void;
  private onConnectionStateChange?: (state: ConnectionState) => void;

  constructor(options: ProxyControllerOptions = {}) {
    this.onError = options.onError;
    this.onConnectionStateChange = options.onConnectionStateChange;

    // Initialize tab communicator
    this.communicator = new TabCommunicator({
      role: 'target',
      onConnectionStateChange: this.handleConnectionStateChange.bind(this),
      onError: this.handleCommunicatorError.bind(this),
    });

    if (options.controlWindow) {
      this.communicator.setTargetWindow(options.controlWindow);
    }

    this.setupNavigationHandling();
    this.log('Target tab proxy controller initialized');
  }

  /**
   * Initialize connection with control tab
   * This replaces the original init method that connected to CDP
   */
  async init(browserWsEndpoint: string, targetId: string): Promise<void> {
    if (this.isInitialized) {
      this.log('Already initialized');
      return;
    }

    try {
      await this.communicator.connect();
      this.isInitialized = true;
      this.log('✓ Target tab proxy controller connected to control tab');
    } catch (error) {
      this.error('Failed to initialize target tab proxy controller:', error);
      throw error;
    }
  }

  /**
   * Set control window for communication
   */
  setControlWindow(window: Window): void {
    this.communicator.setTargetWindow(window);
  }

  /**
   * Setup browser metrics - forwards to control tab
   */
  async setupBrowserMetrics(viewPort: { width: number; height: number }): Promise<void> {
    return await this.communicator.sendRequest('setupBrowserMetrics', viewPort);
  }

  /**
   * Dispatch mouse move event - forwards to control tab
   */
  async dispatchMouseMove(x: number, y: number): Promise<void> {
    return await this.communicator.sendRequest('dispatchMouseMove', { x, y });
  }

  /**
   * Dispatch mouse down event - forwards to control tab
   */
  async dispatchMouseDown(x: number, y: number, button: string = 'left'): Promise<void> {
    return await this.communicator.sendRequest('dispatchMouseDown', { x, y, button });
  }

  /**
   * Dispatch mouse up event - forwards to control tab
   */
  async dispatchMouseUp(x: number, y: number, button: string = 'left'): Promise<void> {
    return await this.communicator.sendRequest('dispatchMouseUp', { x, y, button });
  }

  /**
   * Dispatch mouse click event - forwards to control tab
   */
  async dispatchMouseClick(x: number, y: number, button: string = 'left'): Promise<void> {
    return await this.communicator.sendRequest('dispatchMouseClick', { x, y, button });
  }

  /**
   * Dispatch key event - forwards to control tab
   */
  async dispatchKeyEvent(params: {
    type: string;
    key: string;
    code?: string;
    windowsVirtualKeyCode?: number;
  }): Promise<void> {
    return await this.communicator.sendRequest('dispatchKeyEvent', params);
  }

  /**
   * Insert text - forwards to control tab
   */
  async insertText(text: string): Promise<void> {
    return await this.communicator.sendRequest('insertText', { text });
  }

  /**
   * Take screenshot - forwards to control tab
   */
  async takeScreenshot(quality?: number, format?: string): Promise<string> {
    return await this.communicator.sendRequest('takeScreenshot', { quality, format });
  }

  /**
   * Request new frame - forwards to control tab
   */
  async requestNewFrame(): Promise<void> {
    return await this.communicator.sendRequest('requestNewFrame', {});
  }

  /**
   * Trigger mouse movement - forwards to control tab
   */
  async triggerMouseMovement(): Promise<void> {
    return await this.communicator.sendRequest('triggerMouseMovement', {});
  }

  /**
   * Handle input event - forwards to control tab
   */
  async handleInputEvent(data: any): Promise<void> {
    return await this.communicator.sendRequest('handleInputEvent', data);
  }

  /**
   * Send event to control tab
   */
  async sendEvent(eventType: string, data: any): Promise<void> {
    await this.communicator.sendEvent(eventType as any, data);
  }

  /**
   * Listen for events from control tab
   */
  addEventListener(eventType: string, listener: (data: any) => void): void {
    this.communicator.addEventListener(eventType as any, listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(eventType: string, listener: (data: any) => void): void {
    this.communicator.removeEventListener(eventType as any, listener);
  }

  /**
   * Get connection state
   */
  getConnectionState(): ConnectionState {
    return this.communicator.getConnectionState();
  }

  /**
   * Cleanup and disconnect
   */
  async cleanup(): Promise<void> {
    this.communicator.disconnect();
    this.isInitialized = false;
    this.log('Target tab proxy controller cleaned up');
  }

  private handleConnectionStateChange(state: ConnectionState): void {
    this.log('Connection state changed to:', state);

    if (state === ConnectionState.ERROR) {
      this.handleConnectionError(new Error('Connection lost'));
    } else if (state === ConnectionState.CONNECTED) {
      this.handleConnectionRecovery();
    }

    this.onConnectionStateChange?.(state);
  }

  private handleCommunicatorError(error: Error): void {
    this.error('Communicator error:', error);
    this.handleConnectionError(error);
    this.onError?.(error);
  }

  private log(...args: any[]): void {
    console.log('[TargetTabProxyController]', ...args);
  }

  private error(...args: any[]): void {
    console.error('[TargetTabProxyController]', ...args);
  }

  /**
   * Setup navigation event handling to detect page reloads and recover
   */
  private setupNavigationHandling(): void {
    // Listen for page navigation events
    window.addEventListener('beforeunload', () => {
      this.log('Page is about to unload, cleaning up...');
      this.cleanup();
    });

    // Listen for page load events to re-establish connection
    window.addEventListener('load', () => {
      if (!this.isInitialized) {
        this.log('Page loaded, attempting to re-establish connection...');
        // The connection workflow should re-inject scripts after navigation
      }
    });

    // Listen for visibility changes
    document.addEventListener('visibilitychange', () => {
      if (
        document.visibilityState === 'visible' &&
        this.communicator.getConnectionState() === ConnectionState.ERROR
      ) {
        this.log('Tab became visible, checking connection...');
        // Connection recovery is handled by TabCommunicator
      }
    });
  }

  /**
   * Handle connection errors and attempt recovery
   */
  private handleConnectionError(error: Error): void {
    this.error('Connection error occurred:', error.message);

    // Notify any dependent scripts about the connection issue
    if (window.screenCropper && typeof window.screenCropper.pauseFrameSending === 'function') {
      window.screenCropper.pauseFrameSending();
    }

    // Emit a custom event for other scripts to handle
    window.dispatchEvent(
      new CustomEvent('browser-controller-error', {
        detail: { error: error.message, timestamp: Date.now() },
      }),
    );
  }

  /**
   * Handle successful connection recovery
   */
  private handleConnectionRecovery(): void {
    this.log('Connection recovered successfully');

    // Resume any paused operations
    if (window.screenCropper && typeof window.screenCropper.resumeFrameSending === 'function') {
      window.screenCropper.resumeFrameSending();
    }

    // Emit recovery event
    window.dispatchEvent(
      new CustomEvent('browser-controller-recovered', {
        detail: { timestamp: Date.now() },
      }),
    );
  }
}

// Global initialization function that maintains the same API as the original browser-controller
(function initTargetTabProxyController() {
  let proxyController: TargetTabProxyController | null = null;

  /**
   * Initialize the proxy controller - maintains same signature as original
   */
  async function init(browserWsEndpoint: string, targetId: string): Promise<void> {
    if (proxyController) {
      console.log('Target tab proxy controller already initialized');
      return;
    }

    proxyController = new TargetTabProxyController({
      onError: (error) => console.error('Target tab proxy error:', error),
      onConnectionStateChange: (state) => console.log('Proxy connection state:', state),
    });

    await proxyController.init(browserWsEndpoint, targetId);
  }

  /**
   * Set control window for communication
   */
  function setControlWindow(window: Window): void {
    if (proxyController) {
      proxyController.setControlWindow(window);
    }
  }

  /**
   * Setup browser metrics
   */
  async function setupBrowserMetrics(viewPort: { width: number; height: number }): Promise<void> {
    if (!proxyController) throw new Error('Proxy controller not initialized');
    return await proxyController.setupBrowserMetrics(viewPort);
  }

  /**
   * Dispatch mouse move
   */
  async function dispatchMouseMove(x: number, y: number): Promise<void> {
    if (!proxyController) throw new Error('Proxy controller not initialized');
    return await proxyController.dispatchMouseMove(x, y);
  }

  /**
   * Dispatch mouse down
   */
  async function dispatchMouseDown(x: number, y: number, button?: string): Promise<void> {
    if (!proxyController) throw new Error('Proxy controller not initialized');
    return await proxyController.dispatchMouseDown(x, y, button);
  }

  /**
   * Dispatch mouse up
   */
  async function dispatchMouseUp(x: number, y: number, button?: string): Promise<void> {
    if (!proxyController) throw new Error('Proxy controller not initialized');
    return await proxyController.dispatchMouseUp(x, y, button);
  }

  /**
   * Dispatch mouse click
   */
  async function dispatchMouseClick(x: number, y: number, button?: string): Promise<void> {
    if (!proxyController) throw new Error('Proxy controller not initialized');
    return await proxyController.dispatchMouseClick(x, y, button);
  }

  /**
   * Dispatch key event
   */
  async function dispatchKeyEvent(params: any): Promise<void> {
    if (!proxyController) throw new Error('Proxy controller not initialized');
    return await proxyController.dispatchKeyEvent(params);
  }

  /**
   * Insert text
   */
  async function insertText(text: string): Promise<void> {
    if (!proxyController) throw new Error('Proxy controller not initialized');
    return await proxyController.insertText(text);
  }

  /**
   * Take screenshot
   */
  async function takeScreenshot(quality?: number, format?: string): Promise<string> {
    if (!proxyController) throw new Error('Proxy controller not initialized');
    return await proxyController.takeScreenshot(quality, format);
  }

  /**
   * Handle input event
   */
  async function handleInputEvent(data: any): Promise<void> {
    if (!proxyController) throw new Error('Proxy controller not initialized');
    return await proxyController.handleInputEvent(data);
  }

  /**
   * Request new frame
   */
  async function requestNewFrame(): Promise<void> {
    if (!proxyController) throw new Error('Proxy controller not initialized');
    return await proxyController.requestNewFrame();
  }

  /**
   * Trigger mouse movement
   */
  async function triggerMouseMovement(): Promise<void> {
    if (!proxyController) throw new Error('Proxy controller not initialized');
    return await proxyController.triggerMouseMovement();
  }

  /**
   * Cleanup
   */
  async function cleanup(): Promise<void> {
    if (proxyController) {
      await proxyController.cleanup();
      proxyController = null;
    }
  }

  // Expose the same global API as the original browser-controller
  (globalThis as any).browserController = {
    init,
    setControlWindow,
    setupBrowserMetrics,
    dispatchMouseMove,
    dispatchMouseDown,
    dispatchMouseUp,
    dispatchMouseClick,
    dispatchKeyEvent,
    insertText,
    takeScreenshot,
    handleInputEvent,
    requestNewFrame,
    triggerMouseMovement,
    cleanup,
  };

  // Also expose proxy-specific methods
  (globalThis as any).targetTabProxyController = {
    init,
    setControlWindow,
    cleanup,
    getConnectionState: () => proxyController?.getConnectionState(),
    sendEvent: (eventType: string, data: any) => proxyController?.sendEvent(eventType, data),
    addEventListener: (eventType: string, listener: (data: any) => void) =>
      proxyController?.addEventListener(eventType, listener),
    removeEventListener: (eventType: string, listener: (data: any) => void) =>
      proxyController?.removeEventListener(eventType, listener),
  };
})();
